package main

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	"github.com/adrg/xdg"
)

var (
	driverVersion    = "1.52.0"
	driverCDNMirrors = []string{
		"https://dl.shanzhulab.cn",
		"https://playwright.azureedge.net",
		"https://playwright-akamai.azureedge.net",
		"https://playwright-verizon.azureedge.net",
	}
	browserCDNMirrors = []string{
		"https://registry.npmmirror.com/-/binary/playwright",
		"https://cdn.playwright.dev/dbazure/download/playwright",
		"https://dl.shanzhulab.cn",
	}
)

func InstallPlaywright() error {
	// todo
	return nil
}

func GetDriverPath() (string, error) {
	path, err := getPlaywrightPath()
	if err != nil {
		return "", err
	}
	return filepath.Join(path, "ms-playwright-go", driverVersion), nil
}

func GetBrowserPath() (string, error) {
	path, err := getPlaywrightPath()
	if err != nil {
		return "", err
	}
	return filepath.Join(path, "ms-playwright", "chromium-1169"), nil
}

// Default is user cache directory
func getPlaywrightPath() (string, error) {
	switch runtime.GOOS {
	case "windows":
		return filepath.Join(xdg.Home, "AppData", "Local"), nil
	case "darwin":
		return xdg.CacheHome, nil
	default:
		return "", fmt.Errorf("unsupported os: %s", runtime.GOOS)
	}
}

func GetDriverURLs() []string {
	platform := ""
	switch runtime.GOOS {
	case "windows":
		platform = "win32_x64"
	case "darwin":
		if runtime.GOARCH == "arm64" {
			platform = "mac-arm64"
		} else {
			platform = "mac"
		}
	case "linux":
		if runtime.GOARCH == "arm64" {
			platform = "linux-arm64"
		} else {
			platform = "linux"
		}
	default:
		return []string{}
	}

	baseURLs := []string{}
	pattern := "%s/builds/driver/playwright-%s-%s.zip"

	if hostEnv := os.Getenv("PLAYWRIGHT_DOWNLOAD_HOST"); hostEnv != "" {
		baseURLs = append(baseURLs, fmt.Sprintf(pattern, hostEnv, driverVersion, platform))
	} else {
		for _, mirror := range driverCDNMirrors {
			baseURLs = append(baseURLs, fmt.Sprintf(pattern, mirror, driverVersion, platform))
		}
	}
	return baseURLs
}

func GetBrwoserUrls() []string {
	platform := ""
	switch runtime.GOOS {
	case "windows":
		platform = "win64"
	case "darwin":
		if runtime.GOARCH == "arm64" {
			platform = "mac-arm64"
		}
	default:
		return []string{}
	}

	baseURLs := []string{}
	pattern := "%s/builds/chromium/1169/chromium-%s.zip"

	if hostEnv := os.Getenv("PLAYWRIGHT_DOWNLOAD_HOST"); hostEnv != "" {
		baseURLs = append(baseURLs, fmt.Sprintf(pattern, hostEnv, platform))
	} else {
		for _, mirror := range browserCDNMirrors {
			baseURLs = append(baseURLs, fmt.Sprintf(pattern, mirror, platform))
		}
	}
	return baseURLs
}
