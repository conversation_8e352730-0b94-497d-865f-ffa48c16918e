package main

import (
	"context"
	"fmt"
	"log"
	"strings"

	rt "github.com/wailsapp/wails/v2/pkg/runtime"
	"gorm.io/gorm"
)

var TencentCloudHttpClient *TencentCloudClient

// APIService 处理API请求的服务
type APIService struct {
	ctx context.Context
	db  *gorm.DB
}

// NewAPIService 创建新的API服务实例
func NewAPIService() *APIService {
	return &APIService{db: GetDB()}
}

// startup 初始化上下文
func (a *APIService) Startup(ctx context.Context) {
	a.ctx = ctx

	if a.IsDebugMode() {
		log.Print("应用正在调试模式下运行")
		PREFIX = "http://"
		TencentCloudHttpClient = NewTencentCloudClient("1304480945-0lauw7y7gz.ap-guangzhou.tencentscf.com")
	} else {
		PREFIX = "https://"
		TencentCloudHttpClient = NewTencentCloudClient("1304480945-494z1azhw3.ap-guangzhou.tencentscf.com")
	}
	if TencentCloudHttpClient == nil {
		log.Print("腾讯云客户端初始化失败")
	}
}

// IsDebugMode 检查应用是否在调试模式下运行
func (a *APIService) IsDebugMode() bool {
	// 获取环境信息
	buildType := rt.Environment(a.ctx).BuildType

	// 如果是开发模式，返回true
	isDebug := buildType == "dev"

	if AppLogger != nil {
		AppLogger.Debug("当前运行模式: %s, 是否调试模式: %v", buildType, isDebug)
	}

	return isDebug
}

// LoginAPI 处理登录API请求
func (a *APIService) LoginAPI(email, password string) error {
	if TencentCloudHttpClient == nil {
		return fmt.Errorf("腾讯云客户端未初始化")
	}

	log.Print("使用腾讯云客户端登录")
	_, err := TencentCloudHttpClient.Login(a.ctx, email, password)
	if err != nil {
		log.Printf("腾讯云登录失败: %v", err)
		return err
	}

	return nil
}

// RegisterAPI 处理注册API请求
func (a *APIService) RegisterAPI(email, password string) error {
	if TencentCloudHttpClient == nil {
		return fmt.Errorf("腾讯云客户端未初始化")
	}
	log.Print("使用腾讯云客户端注册")
	err := TencentCloudHttpClient.Register(a.ctx, email, password)
	if err != nil {
		log.Printf("腾讯云注册失败: %v", err)
		return err
	}

	return nil
}

// GetConfig 获取登录后的全局Config
func (a *APIService) GetConfig() []ConfigItem {
	return GetGlobalConfig()
}

// GetSubjects 获取登录后的全局Subjects
func (a *APIService) GetSubjects() []Subject {
	return GetGlobalSubjects()
}

// GetBalance 获取用户积分
func (a *APIService) GetBalance() (int, error) {
	if TencentCloudHttpClient == nil {
		return 0, fmt.Errorf("腾讯云客户端未初始化")
	}

	// 调用API获取余额
	balance, err := TencentCloudHttpClient.GetBalance(a.ctx)
	if err != nil {
		log.Printf("获取余额失败: %v", err)
		return 0, err
	}

	log.Printf("获取积分成功: %d", balance)
	return balance, nil
}

// GradeWithAnalysisMode 使用指定分析模式进行评分
func (a *APIService) GradeWithAnalysisMode(image, prompt, subject, analysisMode string) (*GradingResponse, error) {
	fmt.Printf("subject: %v\n", subject)
	fmt.Printf("prompt: %v\n", prompt)
	fmt.Printf("analysisMode: %v\n", analysisMode)
	log.Print("正在评分")
	if TencentCloudHttpClient == nil {
		return nil, fmt.Errorf("腾讯云客户端未初始化")
	}
	log.Printf("使用腾讯云客户端评分，分析模式: %s", analysisMode)
	data, err := TencentCloudHttpClient.ChatWithAnalysisMode(a.ctx, image, prompt, subject, analysisMode)
	if err != nil {
		log.Printf("腾讯云评分失败: %v", err)
		return nil, err
	}
	log.Print("评分完成")
	// 尝试解析云函数返回的结果
	var gradingResponse GradingResponse
	// 成功解析为GradingResponse
	log.Print("成功解析云函数返回的GradingResponse")
	// 学生答案文本
	gradingResponse.StudentAnswer = data.Analysis.StudentAnswer
	gradingResponse.GradingDetails = data.Analysis.GradingDetails
	// 评分细节在分号和句号后分行
	gradingResponse.GradingDetails = strings.ReplaceAll(gradingResponse.GradingDetails, ";", ";\n")
	gradingResponse.GradingDetails = strings.ReplaceAll(gradingResponse.GradingDetails, "；", "；\n")
	gradingResponse.GradingDetails = strings.ReplaceAll(gradingResponse.GradingDetails, "。", "。\n")
	// 总分
	gradingResponse.Score = data.Analysis.Score
	// 将余额信息传递给前端
	gradingResponse.Balance = data.Balance
	// 保存评分记录到数据库
	go func() {
		// 获取用户邮箱
		userEmail := GetConfig().GetString("app_username")
		if userEmail == "" {
			userEmail = "anonymous"
		}
		// 保存记录
		err := SaveGradingRecord(
			a.db,
			userEmail,
			image,
			prompt,                        // 评分标准
			gradingResponse.StudentAnswer, // 学生答案文本,
			float64(gradingResponse.Score),
			gradingResponse.GradingDetails,
		)
		if err != nil {
			log.Printf("保存评分记录失败: %v", err)
			if AppLogger != nil {
				AppLogger.Error("保存评分记录失败: %v", err)
			}
		}
	}()
	return &gradingResponse, nil
}

// 根据时间范围和评分标准ID导出阅卷记录分析报告
func (a *APIService) GenerateAnalysisReport() {
	// TODO:根据时间范围和评分标准ID导出阅卷记录分析报告
}

// CheckVersion 检查软件版本
// 返回: 版本信息响应, 错误
func (a *APIService) CheckVersion() (*VersionResponse, error) {
	if TencentCloudHttpClient == nil {
		return nil, fmt.Errorf("腾讯云客户端未初始化")
	}

	// 调用API检查版本
	versionInfo, err := TencentCloudHttpClient.CheckVersion(a.ctx)
	if err != nil {
		log.Printf("检查版本失败: %v", err)
		return nil, err
	}

	log.Printf("检查版本成功，最新版本: %s", versionInfo.Version)
	return versionInfo, nil
}

// GetRechargeList 获取充值记录列表
// limit: 每页数量，默认20
// offset: 偏移量，默认0
// 返回: 充值记录列表, 错误
func (a *APIService) GetRechargeList(limit, offset int) (map[string]interface{}, error) {
	if TencentCloudHttpClient == nil {
		return nil, fmt.Errorf("腾讯云客户端未初始化")
	}

	// 如果参数为0，设置默认值
	if limit <= 0 {
		limit = 20
	}

	// 调用API获取充值记录列表
	result, err := TencentCloudHttpClient.GetRechargeList(a.ctx, limit, offset)
	if err != nil {
		log.Printf("获取充值记录列表失败: %v", err)
		return nil, err
	}

	log.Printf("获取充值记录列表成功")
	return result, nil
}

// RecoverPasswordAPI 发送密码重置邮件
// email: 用户邮箱
// 返回: 错误
func (a *APIService) RecoverPasswordAPI(email string) error {
	if TencentCloudHttpClient == nil {
		return fmt.Errorf("腾讯云客户端未初始化")
	}

	log.Print("使用腾讯云客户端发送密码重置邮件")
	response, err := TencentCloudHttpClient.RecoverPassword(a.ctx, email)
	if err != nil {
		log.Printf("发送密码重置邮件失败: %v", err)
		return err
	}

	log.Printf("发送密码重置邮件成功: %s", response.Message)
	return nil
}

// VerifyTokenAPI 验证密码重置令牌
// email: 用户邮箱
// token: 重置令牌
// 返回: 验证响应, 错误
func (a *APIService) VerifyTokenAPI(email, token string) (*VerifyTokenResponse, error) {
	if TencentCloudHttpClient == nil {
		return nil, fmt.Errorf("腾讯云客户端未初始化")
	}
	log.Print("使用腾讯云客户端验证密码重置令牌")
	response, err := TencentCloudHttpClient.VerifyToken(a.ctx, email, token, "recovery")
	if err != nil {
		log.Printf("验证密码重置令牌失败: %v", err)
		return nil, err
	}
	log.Printf("验证密码重置令牌成功: %s", response.Message)
	return response, nil
}

// VerifySignupTokenAPI 验证注册令牌
// email: 用户邮箱
// token: 注册令牌
// 返回: 验证响应, 错误
func (a *APIService) VerifySignupTokenAPI(email, token string) (*VerifyTokenResponse, error) {
	if TencentCloudHttpClient == nil {
		return nil, fmt.Errorf("腾讯云客户端未初始化")
	}
	log.Print("使用腾讯云客户端验证注册令牌")
	response, err := TencentCloudHttpClient.VerifyToken(a.ctx, email, token, "signup")
	if err != nil {
		log.Printf("验证注册令牌失败: %v", err)
		return nil, err
	}
	log.Printf("验证注册令牌成功: %s", response.Message)
	return response, nil
}

// UpdatePasswordAPI 更新用户密码
// password: 新密码
// 返回: 错误
func (a *APIService) UpdatePasswordAPI(password string) error {
	if TencentCloudHttpClient == nil {
		return fmt.Errorf("腾讯云客户端未初始化")
	}
	log.Print("使用腾讯云客户端更新密码")
	response, err := TencentCloudHttpClient.UpdatePassword(a.ctx, password)
	if err != nil {
		log.Printf("更新密码失败: %v", err)
		return err
	}
	log.Printf("更新密码成功: %s", response.Message)
	return nil
}
