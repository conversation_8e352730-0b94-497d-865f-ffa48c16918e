package main

import (
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/nguyenthenguyen/docx"
	rt "github.com/wailsapp/wails/v2/pkg/runtime"
	"github.com/xuri/excelize/v2"
)

// ExportGradingRecordsToWord 将阅卷记录导出为Word文档
// exportDir: 导出目录，如果为空则显示目录选择对话框
// 返回: 错误信息
func (a *App) ExportGradingRecordsToWord(exportDir string) error {
	if a.ctx == nil {
		return fmt.Errorf("应用上下文未初始化")
	}

	// 通知前端开始加载
	a.StartLoading()
	defer a.StopLoading()

	// 获取所有阅卷记录
	db := GetDB()
	if db == nil {
		return fmt.Errorf("数据库未初始化")
	}

	// 获取所有记录
	result := GetGradingRecords(db)
	if result.Error != "" {
		return fmt.Errorf("获取阅卷记录失败: %s", result.Error)
	}

	if len(result.Records) == 0 {
		// 通知前端没有记录可导出
		rt.EventsEmit(a.ctx, "export:noRecords")
		return fmt.Errorf("没有阅卷记录可导出")
	}

	// 如果没有指定导出目录，则显示目录选择对话框
	if exportDir == "" {
		var err error
		exportDir, err = a.SelectExportDirectory()
		if err != nil {
			return fmt.Errorf("选择导出目录失败: %v", err)
		}

		// 如果用户取消选择，使用默认目录
		if exportDir == "" {
			exportDir = a.GetDefaultExportDirectory()
		}
	}

	// 创建导出目录（如果不存在）
	if err := os.MkdirAll(exportDir, 0o755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 创建导出文件名（使用当前时间）
	timestamp := time.Now().Format("20060102-150405")
	exportFilePath := filepath.Join(exportDir, fmt.Sprintf("阅卷记录-%s.docx", timestamp))

	// 使用模板创建Word文档
	// 打开Word模板文件
	r, err := docx.ReadDocxFile("./templates/grading_records_template.docx")
	if err != nil {
		// 如果模板不存在，则创建一个新的Word文档
		if os.IsNotExist(err) {
			// 创建一个基本的Word文档
			return createWordDocumentFromScratch(exportFilePath, result.Records)
		}
		return fmt.Errorf("打开Word模板文件失败: %v", err)
	}
	defer r.Close()

	// 获取文档
	doc := r.Editable()

	// 添加标题
	doc.Replace("{{TITLE}}", "阅卷记录导出", -1)
	doc.Replace("{{EXPORT_TIME}}", time.Now().Format("2006-01-02 15:04:05"), -1)

	// 添加记录内容
	recordsContent := ""
	for i, record := range result.Records {
		recordNumber := i + 1
		createdTime := time.Time(record.CreatedAt).Format("2006-01-02 15:04:05")

		recordContent := fmt.Sprintf(
			"%d. 创建时间: %s\n   用户: %s\n   分数: %.1f\n   评分标准: %s\n   学生答案: %s\n   评分细节: %s\n\n",
			recordNumber,
			createdTime,
			record.UserEmail,
			record.Score,
			record.GradingCriteria,
			record.AnswerText,
			record.ScoreDetails,
		)

		recordsContent += recordContent
	}

	// 替换内容
	doc.Replace("{{RECORDS_CONTENT}}", recordsContent, -1)

	// 保存文档
	if err := doc.WriteToFile(exportFilePath); err != nil {
		return fmt.Errorf("保存Word文档失败: %v", err)
	}

	// 通知前端导出成功
	rt.EventsEmit(a.ctx, "export:success", exportFilePath, exportDir)

	// 打开导出目录
	rt.BrowserOpenURL(a.ctx, fmt.Sprintf("file://%s", exportDir))

	return nil
}

// ExportGradingRecordsToWordByTimeRangeAndCriteria 将指定时间范围和评分标准内的阅卷记录导出为Word文档
// exportDir: 导出目录，如果为空则显示目录选择对话框
// startTime, endTime: 时间范围
// criteriaID: 评分标准ID
// 返回: 错误信息
func (a *App) ExportGradingRecordsToWordByTimeRangeAndCriteria(exportDir string, startTime, endTime time.Time, criteriaID string) error {
	if a.ctx == nil {
		return fmt.Errorf("应用上下文未初始化")
	}

	// 通知前端开始加载
	a.StartLoading()
	defer a.StopLoading()

	// 获取所有阅卷记录
	db := GetDB()
	if db == nil {
		return fmt.Errorf("数据库未初始化")
	}

	// 获取指定时间范围和评分标准的记录
	records := GetGradingRecordsByTimeRangeAndCriteria(db, criteriaID, startTime, endTime)
	if len(records) == 0 {
		// 通知前端没有记录可导出
		rt.EventsEmit(a.ctx, "export:noRecords")
		return fmt.Errorf("指定时间范围和评分标准内没有阅卷记录可导出")
	}

	// 如果没有指定导出目录，则显示目录选择对话框
	if exportDir == "" {
		var err error
		exportDir, err = a.SelectExportDirectory()
		if err != nil {
			return fmt.Errorf("选择导出目录失败: %v", err)
		}

		// 如果用户取消选择，使用默认目录
		if exportDir == "" {
			exportDir = a.GetDefaultExportDirectory()
		}
	}

	// 创建导出目录（如果不存在）
	if err := os.MkdirAll(exportDir, 0o755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 获取评分标准内容
	var criteriaContent string
	var criteria GradingCriteria
	if err := db.Where("id = ?", criteriaID).First(&criteria).Error; err == nil {
		criteriaContent = criteria.Content
	} else {
		criteriaContent = "未知评分标准"
	}

	// 创建导出文件名（使用当前时间、时间范围和评分标准）
	timestamp := time.Now().Format("20060102-150405")
	timeRangeStr := fmt.Sprintf("%s至%s", startTime.Format("0102-1504"), endTime.Format("0102-1504"))
	criteriaShort := criteriaContent
	if len(criteriaShort) > 20 {
		criteriaShort = criteriaShort[:20] + "..."
	}
	exportFilePath := filepath.Join(exportDir, fmt.Sprintf("阅卷记录-%s-%s-%s.docx", timeRangeStr, criteriaShort, timestamp))

	// 使用模板创建Word文档
	// 打开Word模板文件
	r, err := docx.ReadDocxFile("./templates/grading_records_template.docx")
	if err != nil {
		// 如果模板不存在，则创建一个新的Word文档
		if os.IsNotExist(err) {
			// 创建一个基本的Word文档
			return createWordDocumentFromScratch(exportFilePath, records)
		}
		return fmt.Errorf("打开Word模板文件失败: %v", err)
	}
	defer r.Close()

	// 获取文档
	doc := r.Editable()

	// 添加标题
	doc.Replace("{{TITLE}}", fmt.Sprintf("阅卷记录导出 (%s, %s)", timeRangeStr, criteriaShort), -1)
	doc.Replace("{{EXPORT_TIME}}", time.Now().Format("2006-01-02 15:04:05"), -1)

	// 添加记录内容
	recordsContent := ""
	for i, record := range records {
		recordNumber := i + 1
		createdTime := time.Time(record.CreatedAt).Format("2006-01-02 15:04:05")

		recordContent := fmt.Sprintf(
			"%d. 创建时间: %s\n   用户: %s\n   分数: %.1f\n   评分标准: %s\n   学生答案: %s\n   评分细节: %s\n\n",
			recordNumber,
			createdTime,
			record.UserEmail,
			record.Score,
			record.GradingCriteria,
			record.AnswerText,
			record.ScoreDetails,
		)

		recordsContent += recordContent
	}

	// 替换内容
	doc.Replace("{{RECORDS_CONTENT}}", recordsContent, -1)

	// 保存文档
	if err := doc.WriteToFile(exportFilePath); err != nil {
		return fmt.Errorf("保存Word文档失败: %v", err)
	}

	// 通知前端导出成功
	rt.EventsEmit(a.ctx, "export:success", exportFilePath, exportDir)

	// 打开导出目录
	rt.BrowserOpenURL(a.ctx, fmt.Sprintf("file://%s", exportDir))

	return nil
}

// ExportGradingRecordsToWordByTimeRange 将指定时间范围内的阅卷记录导出为Word文档
// exportDir: 导出目录，如果为空则显示目录选择对话框
// startTime, endTime: 时间范围
// 返回: 错误信息
func (a *App) ExportGradingRecordsToWordByTimeRange(exportDir string, startTime, endTime time.Time) error {
	if a.ctx == nil {
		return fmt.Errorf("应用上下文未初始化")
	}

	// 通知前端开始加载
	a.StartLoading()
	defer a.StopLoading()

	// 获取所有阅卷记录
	db := GetDB()
	if db == nil {
		return fmt.Errorf("数据库未初始化")
	}

	// 获取指定时间范围内的记录
	records := GetGradingRecordsByTimeRange(db, startTime, endTime)
	if len(records) == 0 {
		// 通知前端没有记录可导出
		rt.EventsEmit(a.ctx, "export:noRecords")
		return fmt.Errorf("指定时间范围内没有阅卷记录可导出")
	}

	// 如果没有指定导出目录，则显示目录选择对话框
	if exportDir == "" {
		var err error
		exportDir, err = a.SelectExportDirectory()
		if err != nil {
			return fmt.Errorf("选择导出目录失败: %v", err)
		}

		// 如果用户取消选择，使用默认目录
		if exportDir == "" {
			exportDir = a.GetDefaultExportDirectory()
		}
	}

	// 创建导出目录（如果不存在）
	if err := os.MkdirAll(exportDir, 0o755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 创建导出文件名（使用当前时间和时间范围）
	timestamp := time.Now().Format("20060102-150405")
	timeRangeStr := fmt.Sprintf("%s至%s", startTime.Format("0102-1504"), endTime.Format("0102-1504"))
	exportFilePath := filepath.Join(exportDir, fmt.Sprintf("阅卷记录-%s-%s.docx", timeRangeStr, timestamp))

	// 使用模板创建Word文档
	// 打开Word模板文件
	r, err := docx.ReadDocxFile("./templates/grading_records_template.docx")
	if err != nil {
		// 如果模板不存在，则创建一个新的Word文档
		if os.IsNotExist(err) {
			// 创建一个基本的Word文档
			return createWordDocumentFromScratch(exportFilePath, records)
		}
		return fmt.Errorf("打开Word模板文件失败: %v", err)
	}
	defer r.Close()

	// 获取文档
	doc := r.Editable()

	// 添加标题
	doc.Replace("{{TITLE}}", fmt.Sprintf("阅卷记录导出 (%s)", timeRangeStr), -1)
	doc.Replace("{{EXPORT_TIME}}", time.Now().Format("2006-01-02 15:04:05"), -1)

	// 添加记录内容
	recordsContent := ""
	for i, record := range records {
		recordNumber := i + 1
		createdTime := time.Time(record.CreatedAt).Format("2006-01-02 15:04:05")

		recordContent := fmt.Sprintf(
			"%d. 创建时间: %s\n   用户: %s\n   分数: %.1f\n   评分标准: %s\n   学生答案: %s\n   评分细节: %s\n\n",
			recordNumber,
			createdTime,
			record.UserEmail,
			record.Score,
			record.GradingCriteria,
			record.AnswerText,
			record.ScoreDetails,
		)

		recordsContent += recordContent
	}

	// 替换内容
	doc.Replace("{{RECORDS_CONTENT}}", recordsContent, -1)

	// 保存文档
	if err := doc.WriteToFile(exportFilePath); err != nil {
		return fmt.Errorf("保存Word文档失败: %v", err)
	}

	// 通知前端导出成功
	rt.EventsEmit(a.ctx, "export:success", exportFilePath, exportDir)

	// 打开导出目录
	rt.BrowserOpenURL(a.ctx, fmt.Sprintf("file://%s", exportDir))

	return nil
}

// createWordDocumentFromScratch 从头创建Word文档（不使用模板）
func createWordDocumentFromScratch(filePath string, records []GradingRecordListItem) error {
	// 创建一个新的Word文档
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建Word文档失败: %v", err)
	}
	defer file.Close()

	// 创建一个简单的XML格式的Word文档
	content := `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?mso-application progid="Word.Document"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <w:body>
    <w:p>
      <w:r>
        <w:t>阅卷记录导出</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>导出时间: ` + time.Now().Format("2006-01-02 15:04:05") + `</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>记录总数: ` + fmt.Sprintf("%d", len(records)) + `</w:t>
      </w:r>
    </w:p>`

	// 添加记录内容
	for i, record := range records {
		recordNumber := i + 1
		createdTime := time.Time(record.CreatedAt).Format("2006-01-02 15:04:05")

		content += fmt.Sprintf(`
    <w:p>
      <w:r>
        <w:t>%d. 创建时间: %s</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>   用户: %s</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>   分数: %.1f</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>   评分标准: %s</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>   学生答案: %s</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>   评分细节: %s</w:t>
      </w:r>
    </w:p>`,
			recordNumber,
			createdTime,
			record.UserEmail,
			record.Score,
			record.GradingCriteria,
			record.AnswerText,
			record.ScoreDetails,
		)
	}

	content += `
  </w:body>
</w:document>`

	// 写入文件
	_, err = file.WriteString(content)
	if err != nil {
		return fmt.Errorf("写入Word文档内容失败: %v", err)
	}

	return nil
}

// base64ToImageBytes 将base64编码的图片转换为字节数组
// 参数:
// - base64Str: base64编码的图片字符串
// 返回:
// - 图片字节数组
// - 错误信息
func base64ToImageBytes(base64Str string) ([]byte, error) {
	// 如果base64字符串包含前缀(如data:image/png;base64,)，则去除前缀
	if idx := strings.Index(base64Str, ","); idx != -1 {
		base64Str = base64Str[idx+1:]
	}

	// 解码base64字符串为字节数组
	return base64.StdEncoding.DecodeString(base64Str)
}

// ExportGradingRecordsToExcel 将阅卷记录导出为Excel表格
// exportDir: 导出目录，如果为空则显示目录选择对话框
// 返回: 错误信息
func (a *App) ExportGradingRecordsToExcel(exportDir string) error {
	if a.ctx == nil {
		return fmt.Errorf("应用上下文未初始化")
	}

	// 通知前端开始加载
	a.StartLoading()
	defer a.StopLoading()

	// 获取所有阅卷记录
	db := GetDB()
	if db == nil {
		return fmt.Errorf("数据库未初始化")
	}

	// 获取所有记录
	result := GetGradingRecords(db)
	if result.Error != "" {
		return fmt.Errorf("获取阅卷记录失败: %s", result.Error)
	}

	if len(result.Records) == 0 {
		// 通知前端没有记录可导出
		rt.EventsEmit(a.ctx, "export:noRecords")
		return fmt.Errorf("没有阅卷记录可导出")
	}

	// 如果没有指定导出目录，则显示目录选择对话框
	if exportDir == "" {
		var err error
		exportDir, err = a.SelectExportDirectory()
		if err != nil {
			return fmt.Errorf("选择导出目录失败: %v", err)
		}

		// 如果用户取消选择，使用默认目录
		if exportDir == "" {
			exportDir = a.GetDefaultExportDirectory()
		}
	}

	// 创建导出目录（如果不存在）
	if err := os.MkdirAll(exportDir, 0o755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 创建导出文件名（使用当前时间）
	timestamp := time.Now().Format("20060102-150405")
	exportFilePath := filepath.Join(exportDir, fmt.Sprintf("阅卷记录-%s.xlsx", timestamp))

	// 创建一个新的Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// 创建一个工作表
	sheetName := "阅卷记录"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return fmt.Errorf("创建工作表失败: %v", err)
	}

	// 设置列宽
	f.SetColWidth(sheetName, "A", "A", 20) // 创建时间
	f.SetColWidth(sheetName, "B", "B", 20) // 用户
	f.SetColWidth(sheetName, "C", "C", 10) // 分数
	f.SetColWidth(sheetName, "D", "D", 40) // 评分标准
	f.SetColWidth(sheetName, "E", "E", 40) // 学生答案
	f.SetColWidth(sheetName, "F", "F", 40) // 评分细节
	f.SetColWidth(sheetName, "G", "G", 20) // 答案图片

	// 设置标题行样式
	titleStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
			Size: 12,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#DCE6F1"},
		},
	})
	if err != nil {
		return fmt.Errorf("创建样式失败: %v", err)
	}

	// 设置标题行
	f.SetCellValue(sheetName, "A1", "创建时间")
	f.SetCellValue(sheetName, "B1", "用户")
	f.SetCellValue(sheetName, "C1", "分数")
	f.SetCellValue(sheetName, "D1", "评分标准")
	f.SetCellValue(sheetName, "E1", "学生答案")
	f.SetCellValue(sheetName, "F1", "评分细节")
	f.SetCellValue(sheetName, "G1", "答案图片")

	// 应用标题行样式
	f.SetCellStyle(sheetName, "A1", "G1", titleStyle)

	// 填充数据
	for i, record := range result.Records {
		row := i + 2 // 从第2行开始（第1行是标题）
		createdTime := time.Time(record.CreatedAt).Format("2006-01-02 15:04:05")

		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), createdTime)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), record.UserEmail)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), record.Score)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), record.GradingCriteria)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), record.AnswerText)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), record.ScoreDetails)

		// 将答案图片插入到Excel中
		if record.AnswerImage != "" {
			// 将base64图片转换为实际图片并插入
			imageData, err := base64ToImageBytes(record.AnswerImage)
			if err != nil {
				f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), "[图片解析失败]")
				continue
			}

			// 设置单元格高度以容纳图片
			f.SetRowHeight(sheetName, row, 120)

			// 添加图片到单元格
			if err := f.AddPictureFromBytes(sheetName, fmt.Sprintf("G%d", row),
				&excelize.Picture{
					Extension: ".png",
					File:      imageData,
					Format: &excelize.GraphicOptions{
						AutoFit:         true,
						ScaleX:          1.5,
						ScaleY:          1.5,
						LockAspectRatio: true,
					},
				},
			); err != nil {
				f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), "[图片插入失败]")
			}
		}
	}

	// 设置活动工作表
	f.SetActiveSheet(index)

	// 保存Excel文件
	if err := f.SaveAs(exportFilePath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	// 通知前端导出成功
	rt.EventsEmit(a.ctx, "export:success", exportFilePath, exportDir)

	// 打开导出目录
	rt.BrowserOpenURL(a.ctx, fmt.Sprintf("file://%s", exportDir))

	return nil
}

// ExportGradingRecordsToExcelByTimeRange 将指定时间范围内的阅卷记录导出为Excel表格
// exportDir: 导出目录，如果为空则显示目录选择对话框
// startTime, endTime: 时间范围
// hasReport: 是否需要导出报告
// 返回: 错误信息
func (a *App) ExportGradingRecordsToExcelByTimeRange(exportDir string, startTime, endTime time.Time, hasReport bool) error {
	if a.ctx == nil {
		return fmt.Errorf("应用上下文未初始化")
	}
	// 通知前端开始加载
	a.StartLoading()
	defer a.StopLoading()
	// 获取所有阅卷记录
	db := GetDB()
	if db == nil {
		return fmt.Errorf("数据库未初始化")
	}
	// 获取指定时间范围内的记录
	records := GetGradingRecordsByTimeRange(db, startTime, endTime)
	if len(records) == 0 {
		// 通知前端没有记录可导出
		rt.EventsEmit(a.ctx, "export:noRecords")
		return fmt.Errorf("指定时间范围内没有阅卷记录可导出")
	}
	// 如果没有指定导出目录，则显示目录选择对话框
	if exportDir == "" {
		var err error
		exportDir, err = a.SelectExportDirectory()
		if err != nil {
			return fmt.Errorf("选择导出目录失败: %v", err)
		}
		// 如果用户取消选择，使用默认目录
		if exportDir == "" {
			exportDir = a.GetDefaultExportDirectory()
		}
	}
	// 创建导出目录（如果不存在）
	if err := os.MkdirAll(exportDir, 0o755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}
	// 假如hasReport为true，则异步AI分析并导出报告,等待AI分析完成
	// 创建一个通道，用于等待报告生成完成
	done := make(chan bool)
	// 如果需要报告，则异步生成
	if hasReport {
		go func(done chan bool, records []GradingRecordListItem) {
			// 生成分析报告
			markdownContent := GradingRecordsToMarkdown(records)
			fmt.Printf("markdownContent:\n %v\n", markdownContent)
			response, err := TencentCloudHttpClient.ChatV2(a.ctx, TencentCloudChatV2Request{
				Text:        markdownContent,
				PromptKey:   "grading_records_analysis",
				ContentType: "text",
				Temperature: 0.6,
			})
			if err != nil {
				fmt.Printf("AI分析失败: %v", err)
			} else {
				// 保存AI分析结果到exportDir目录的md文件
				fmt.Printf("AI分析结果: %v", response.Analysis.GradingDetails)

				// 创建分析报告文件名
				timestamp := time.Now().Format("20060102-150405")
				timeRangeStr := fmt.Sprintf("%s至%s", startTime.Format("0102-1504"), endTime.Format("0102-1504"))
				reportFilePath := filepath.Join(exportDir, fmt.Sprintf("分析报告-%s-%s.md", timeRangeStr, timestamp))

				// 创建报告内容
				reportContent := "# 阅卷记录分析报告\n\n"
				reportContent += "## 基本信息\n\n"
				reportContent += fmt.Sprintf("- **生成时间**: %s\n", time.Now().Format("2006-01-02 15:04:05"))
				reportContent += fmt.Sprintf("- **时间范围**: %s 至 %s\n", startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
				reportContent += fmt.Sprintf("- **记录数量**: %d\n\n", len(records))
				reportContent += fmt.Sprintf("## 分析结果\n\n%s\n", response.Analysis.GradingDetails)

				// 写入文件
				if err := os.WriteFile(reportFilePath, []byte(reportContent), 0o644); err != nil {
					fmt.Printf("保存分析报告失败: %v\n", err)
				} else {
					fmt.Printf("分析报告已保存到: %s\n", reportFilePath)
				}
			}
			done <- true
		}(done, records)
	} else {
		// 如果不需要报告，直接发送完成信号
		done <- true
	}
	// 创建导出文件名（使用当前时间和时间范围）
	timestamp := time.Now().Format("20060102-150405")
	timeRangeStr := fmt.Sprintf("%s至%s", startTime.Format("0102-1504"), endTime.Format("0102-1504"))
	exportFilePath := filepath.Join(exportDir, fmt.Sprintf("阅卷记录-%s-%s.xlsx", timeRangeStr, timestamp))
	// 创建一个新的Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	// 创建一个工作表
	sheetName := "阅卷记录"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return fmt.Errorf("创建工作表失败: %v", err)
	}
	// 设置列宽
	f.SetColWidth(sheetName, "A", "A", 20) // 创建时间
	f.SetColWidth(sheetName, "B", "B", 20) // 用户
	f.SetColWidth(sheetName, "C", "C", 10) // 分数
	f.SetColWidth(sheetName, "D", "D", 40) // 评分标准
	f.SetColWidth(sheetName, "E", "E", 40) // 学生答案
	f.SetColWidth(sheetName, "F", "F", 40) // 评分细节
	f.SetColWidth(sheetName, "G", "G", 20) // 答案图片
	// 设置标题行样式
	titleStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
			Size: 12,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#DCE6F1"},
		},
	})
	if err != nil {
		return fmt.Errorf("创建样式失败: %v", err)
	}
	// 设置标题行
	f.SetCellValue(sheetName, "A1", "创建时间")
	f.SetCellValue(sheetName, "B1", "用户")
	f.SetCellValue(sheetName, "C1", "分数")
	f.SetCellValue(sheetName, "D1", "评分标准")
	f.SetCellValue(sheetName, "E1", "学生答案")
	f.SetCellValue(sheetName, "F1", "评分细节")
	f.SetCellValue(sheetName, "G1", "答案图片")
	// 应用标题行样式
	f.SetCellStyle(sheetName, "A1", "G1", titleStyle)
	// 填充数据
	for i, record := range records {
		row := i + 2 // 从第2行开始（第1行是标题）
		createdTime := time.Time(record.CreatedAt).Format("2006-01-02 15:04:05")
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), createdTime)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), record.UserEmail)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), record.Score)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), record.GradingCriteria)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), record.AnswerText)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), record.ScoreDetails)
		// 将答案图片插入到Excel中
		if record.AnswerImage != "" {
			// 将base64图片转换为实际图片并插入
			imageData, err := base64ToImageBytes(record.AnswerImage)
			if err != nil {
				f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), "[图片解析失败]")
				continue
			}
			// 设置单元格高度以容纳图片
			f.SetRowHeight(sheetName, row, 120)
			// 添加图片到单元格
			if err := f.AddPictureFromBytes(sheetName, fmt.Sprintf("G%d", row),
				&excelize.Picture{
					Extension: ".png",
					File:      imageData,
					Format: &excelize.GraphicOptions{
						AutoFit:         true,
						ScaleX:          1.5,
						ScaleY:          1.5,
						LockAspectRatio: true,
					},
				},
			); err != nil {
				f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), "[图片插入失败]")
			}
		}
	}
	// 设置活动工作表
	f.SetActiveSheet(index)
	// 保存Excel文件
	if err := f.SaveAs(exportFilePath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}
	if hasReport {
		<-done
	}
	// 通知前端导出成功
	rt.EventsEmit(a.ctx, "export:success", exportFilePath, exportDir)
	// 打开导出目录
	rt.BrowserOpenURL(a.ctx, fmt.Sprintf("file://%s", exportDir))
	return nil
}

// ExportGradingRecordsToExcelByTimeRangeAndCriteria 将指定时间范围和评分标准内的阅卷记录导出为Excel表格
// exportDir: 导出目录，如果为空则显示目录选择对话框
// startTime, endTime: 时间范围
// criteriaID: 评分标准ID
// hasReport: 是否需要导出报告
// 返回: 错误信息
func (a *App) ExportGradingRecordsToExcelByTimeRangeAndCriteria(exportDir string, startTime, endTime time.Time, criteriaID string, hasReport bool) error {
	if a.ctx == nil {
		return fmt.Errorf("应用上下文未初始化")
	}
	// 通知前端开始加载
	a.StartLoading()
	defer a.StopLoading()
	// 获取所有阅卷记录
	db := GetDB()
	if db == nil {
		return fmt.Errorf("数据库未初始化")
	}
	// 获取指定时间范围和评分标准的记录
	records := GetGradingRecordsByTimeRangeAndCriteria(db, criteriaID, startTime, endTime)
	if len(records) == 0 {
		// 通知前端没有记录可导出
		rt.EventsEmit(a.ctx, "export:noRecords")
		return fmt.Errorf("指定时间范围和评分标准内没有阅卷记录可导出")
	}

	// 如果没有指定导出目录，则显示目录选择对话框
	if exportDir == "" {
		var err error
		exportDir, err = a.SelectExportDirectory()
		if err != nil {
			return fmt.Errorf("选择导出目录失败: %v", err)
		}
		// 如果用户取消选择，使用默认目录
		if exportDir == "" {
			exportDir = a.GetDefaultExportDirectory()
		}
	}
	// 创建导出目录（如果不存在）
	if err := os.MkdirAll(exportDir, 0o755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}
	// 获取评分标准内容
	var criteriaContent string
	var criteria GradingCriteria
	if err := db.Where("id = ?", criteriaID).First(&criteria).Error; err == nil {
		criteriaContent = criteria.Content
	} else {
		criteriaContent = "未知评分标准"
	}

	// 假如hasReport为true，则异步AI分析并导出报告,等待AI分析完成
	// 创建一个通道，用于等待报告生成完成
	done := make(chan bool)
	// 如果需要报告，则异步生成
	if hasReport {
		// 创建一个副本，以便在goroutine中使用
		criteriaContentCopy := criteriaContent

		go func(done chan bool, records []GradingRecordListItem, criteriaContent string) {
			// 生成分析报告
			markdownContent := GradingRecordsToMarkdown(records)
			fmt.Printf("markdownContent:\n %v\n", markdownContent)
			response, err := TencentCloudHttpClient.ChatV2(a.ctx, TencentCloudChatV2Request{
				Text:        markdownContent,
				PromptKey:   "grading_records_analysis",
				ContentType: "text",
				Temperature: 0.6,
			})
			if err != nil {
				fmt.Printf("AI分析失败: %v", err)
			} else {
				// 保存AI分析结果到exportDir目录的md文件
				fmt.Printf("AI分析结果: %v", response.Analysis.GradingDetails)

				// 创建分析报告文件名
				timestamp := time.Now().Format("20060102-150405")
				timeRangeStr := fmt.Sprintf("%s至%s", startTime.Format("0102-1504"), endTime.Format("0102-1504"))
				criteriaShort := criteriaContent
				if len(criteriaShort) > 20 {
					criteriaShort = criteriaShort[:20] + "..."
				}
				reportFilePath := filepath.Join(exportDir, fmt.Sprintf("分析报告-%s-%s-%s.md", timeRangeStr, criteriaShort, timestamp))

				// 创建报告内容
				reportContent := "# 阅卷记录分析报告\n\n"
				reportContent += "## 基本信息\n\n"
				reportContent += fmt.Sprintf("- **生成时间**: %s\n", time.Now().Format("2006-01-02 15:04:05"))
				reportContent += fmt.Sprintf("- **时间范围**: %s 至 %s\n", startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
				reportContent += fmt.Sprintf("- **评分标准**: %s\n", criteriaContent)
				reportContent += fmt.Sprintf("- **记录数量**: %d\n\n", len(records))
				reportContent += fmt.Sprintf("## 分析结果\n\n%s\n", response.Analysis.GradingDetails)

				// 写入文件
				if err := os.WriteFile(reportFilePath, []byte(reportContent), 0o644); err != nil {
					fmt.Printf("保存分析报告失败: %v\n", err)
				} else {
					fmt.Printf("分析报告已保存到: %s\n", reportFilePath)
				}
			}
			done <- true
		}(done, records, criteriaContentCopy)
	} else {
		// 如果不需要报告，直接发送完成信号
		done <- true
	}
	// 创建导出文件名（使用当前时间、时间范围和评分标准）
	timestamp := time.Now().Format("20060102-150405")
	timeRangeStr := fmt.Sprintf("%s至%s", startTime.Format("0102-1504"), endTime.Format("0102-1504"))
	criteriaShort := criteriaContent
	if len(criteriaShort) > 20 {
		criteriaShort = criteriaShort[:20] + "..."
	}
	exportFilePath := filepath.Join(exportDir, fmt.Sprintf("阅卷记录-%s-%s-%s.xlsx", timeRangeStr, criteriaShort, timestamp))
	// 创建一个新的Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	// 创建一个工作表
	sheetName := "阅卷记录"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return fmt.Errorf("创建工作表失败: %v", err)
	}
	// 设置列宽
	f.SetColWidth(sheetName, "A", "A", 20) // 创建时间
	f.SetColWidth(sheetName, "B", "B", 20) // 用户
	f.SetColWidth(sheetName, "C", "C", 10) // 分数
	f.SetColWidth(sheetName, "D", "D", 40) // 评分标准
	f.SetColWidth(sheetName, "E", "E", 40) // 学生答案
	f.SetColWidth(sheetName, "F", "F", 40) // 评分细节
	f.SetColWidth(sheetName, "G", "G", 20) // 答案图片
	// 设置标题行样式
	titleStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
			Size: 12,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#DCE6F1"},
		},
	})
	if err != nil {
		return fmt.Errorf("创建样式失败: %v", err)
	}
	// 设置标题行
	f.SetCellValue(sheetName, "A1", "创建时间")
	f.SetCellValue(sheetName, "B1", "用户")
	f.SetCellValue(sheetName, "C1", "分数")
	f.SetCellValue(sheetName, "D1", "评分标准")
	f.SetCellValue(sheetName, "E1", "学生答案")
	f.SetCellValue(sheetName, "F1", "评分细节")
	f.SetCellValue(sheetName, "G1", "答案图片")
	// 应用标题行样式
	f.SetCellStyle(sheetName, "A1", "G1", titleStyle)
	// 填充数据
	for i, record := range records {
		row := i + 2 // 从第2行开始（第1行是标题）
		createdTime := time.Time(record.CreatedAt).Format("2006-01-02 15:04:05")
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), createdTime)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), record.UserEmail)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), record.Score)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), record.GradingCriteria)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), record.AnswerText)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), record.ScoreDetails)
		// 将答案图片插入到Excel中
		if record.AnswerImage != "" {
			// 将base64图片转换为实际图片并插入
			imageData, err := base64ToImageBytes(record.AnswerImage)
			if err != nil {
				f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), "[图片解析失败]")
				continue
			}
			// 设置单元格高度以容纳图片
			f.SetRowHeight(sheetName, row, 120)
			// 添加图片到单元格
			if err := f.AddPictureFromBytes(sheetName, fmt.Sprintf("G%d", row),
				&excelize.Picture{
					Extension: ".png",
					File:      imageData,
					Format: &excelize.GraphicOptions{
						AutoFit:         true,
						ScaleX:          1.5,
						ScaleY:          1.5,
						LockAspectRatio: true,
					},
				},
			); err != nil {
				f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), "[图片插入失败]")
			}
		}
	}
	// 设置活动工作表
	f.SetActiveSheet(index)
	// 保存Excel文件
	if err := f.SaveAs(exportFilePath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}
	if hasReport {
		<-done
	}
	// 通知前端导出成功
	rt.EventsEmit(a.ctx, "export:success", exportFilePath, exportDir)
	// 打开导出目录
	rt.BrowserOpenURL(a.ctx, fmt.Sprintf("file://%s", exportDir))
	return nil
}
