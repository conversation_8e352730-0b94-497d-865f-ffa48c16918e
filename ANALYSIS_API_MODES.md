# Analysis API 三种模式说明

## 概述

`/api/v2/chat/analysis` 接口现在支持三种不同的分析模式，以满足不同的使用场景和成本需求。

## 三种分析模式

### 1. 专业精批模式 (Professional Mode)

- **模式标识**: `"professional"`
- **工作流程**:
  1. 使用 `doubao-1-5-vision-pro-32k-250115` 进行 OCR 识别
  2. 使用 `deepseek-v3-250324` 进行文本分析
- **特点**:
  - 最高精度的 OCR 识别
  - 最强的分析能力
  - 两步处理，成本最高
  - 返回 OCR 结果和分析结果
- **适用场景**: 重要考试、精确评分、复杂题目分析

### 2. 智能均衡模式 (Standard/Smart Mode)

- **模式标识**: `"standard"`
- **工作流程**:
  - 直接使用 `doubao-seed-1-6-250615` 进行多模态分析
- **特点**:
  - 平衡的性能和成本
  - 一步完成图像理解和分析
  - 中等成本
  - 不返回单独的 OCR 结果
- **适用场景**: 日常作业批改、一般性评估

### 3. 经济极速模式 (Economy/Express Mode)

- **模式标识**: `"economy"`
- **工作流程**:
  - 直接使用 `doubao-seed-1-6-flash-250615` 进行快速多模态分析
- **特点**:
  - 最快的处理速度
  - 最低的成本
  - 适合快速预览和初步评估
  - 不返回单独的 OCR 结果
- **适用场景**: 快速预览、批量处理、成本敏感场景

## API 请求格式

### 请求示例

```json
{
  "content": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/...",
  "text": "评分标准：1. 答案正确性 2. 解题步骤 3. 书写规范",
  "prompt_key": "math_analysis",
  "analysis_mode": "professional",
  "temperature": 0.3
}
```

### 请求参数说明

| 参数            | 类型   | 必填 | 说明                          |
| --------------- | ------ | ---- | ----------------------------- |
| `content`       | string | 是   | 图片的 base64 编码            |
| `text`          | string | 是   | 评分标准或分析要求            |
| `prompt_key`    | string | 是   | 系统提示词的键值              |
| `analysis_mode` | string | 否   | 分析模式，默认为 `"standard"` |
| `temperature`   | float  | 否   | 温度参数，默认为 0.3          |

### 分析模式取值

- `"professional"` - 专业精批模式
- `"standard"` - 智能均衡模式（默认）
- `"economy"` - 经济极速模式

## API 响应格式

### 响应示例

**统一响应格式**（所有三种模式）：

#### 专业精批模式响应

```json
{
  "id": "chatcmpl-xxx",
  "analysis": {
    "student_answer": "学生的手写答案内容：x = 2, y = 3, 因为 2x + y = 7",
    "score": 85,
    "grading_details": "答案正确(50/50)，步骤完整(25/30)，书写清晰(10/20)。建议改进书写规范。"
  },
  "balance": 9500
}
```

#### 智能均衡模式响应

```json
{
  "id": "chatcmpl-xxx",
  "analysis": {
    "student_answer": "x = 2, y = 3, 解题过程：2x + y = 7",
    "score": 80,
    "grading_details": "答案正确，解题思路清晰，得分80分。"
  },
  "balance": 9700
}
```

#### 经济极速模式响应

```json
{
  "id": "chatcmpl-xxx",
  "analysis": {
    "student_answer": "答案基本正确",
    "score": 75,
    "grading_details": "作业已完成，答案大致正确。"
  },
  "balance": 9900
}
```

### 响应参数说明

| 参数                       | 类型   | 说明                                                            |
| -------------------------- | ------ | --------------------------------------------------------------- |
| `id`                       | string | 请求的唯一标识                                                  |
| `analysis`                 | object | 结构化分析结果                                                  |
| `analysis.student_answer`  | string | 学生答案内容（专业模式为 OCR 结果，其他模式为从图片提取的内容） |
| `analysis.score`           | int    | 计算得到的最终数字得分                                          |
| `analysis.grading_details` | string | 详细的评分说明                                                  |
| `balance`                  | int    | 用户余额（积分）                                                |

### 统一结构化输出格式

所有三种模式都使用相同的输出结构：

```json
{
  "student_answer": "学生答案内容",
  "score": 85,
  "grading_details": "详细的评分说明"
}
```

**说明**：

- **专业模式**: `student_answer` 字段包含 OCR 识别的学生答案内容
- **标准/经济模式**: `student_answer` 字段包含从图片中直接提取的学生答案内容
- 所有模式都包含相同的 `score` 和 `grading_details` 字段

## 成本对比

| 模式     | 模型数量 | 相对成本 | 处理时间 | 精度 |
| -------- | -------- | -------- | -------- | ---- |
| 专业精批 | 2 个模型 | 最高     | 较慢     | 最高 |
| 智能均衡 | 1 个模型 | 中等     | 中等     | 中等 |
| 经济极速 | 1 个模型 | 最低     | 最快     | 较低 |

## 使用建议

1. **专业精批模式**: 用于重要考试、精确评分需求
2. **智能均衡模式**: 用于日常作业批改、一般评估
3. **经济极速模式**: 用于快速预览、批量处理

## 错误处理

如果提供了无效的 `analysis_mode`，API 将返回 400 错误：

```json
{
  "error": "Invalid analysis_mode. Must be one of: professional, standard, economy"
}
```

## 兼容性说明

- 如果不提供 `analysis_mode` 参数，默认使用 `"standard"` 模式
- 旧版本的客户端可以继续正常工作，会自动使用智能均衡模式
- 新增的响应字段 `analysis_mode` 和 `models` 不会影响现有客户端的解析
